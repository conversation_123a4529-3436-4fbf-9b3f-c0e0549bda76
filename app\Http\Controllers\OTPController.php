<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\OtpConfiguration;
use App\Models\BusinessSetting;

class OTPController extends Controller
{
    /**
     * Show OTP configuration page
     */
    public function configure_index()
    {
        $otpConfigurations = OtpConfiguration::all();
        return view('backend.setup_configurations.otp_configuration', compact('otpConfigurations'));
    }

    /**
     * Show OTP credentials configuration page
     */
    public function credentials_index()
    {
        return view('backend.setup_configurations.otp_credentials');
    }

    /**
     * Update OTP activation settings
     */
    public function updateActivationSettings(Request $request)
    {
        $otpConfiguration = OtpConfiguration::where('type', $request->type)->first();
        
        if ($otpConfiguration) {
            // Deactivate all other OTP providers
            OtpConfiguration::where('type', '!=', $request->type)->update(['value' => 0]);
            
            // Activate the selected provider
            $otpConfiguration->value = $request->value;
            $otpConfiguration->save();
        }

        flash(translate('OTP configuration updated successfully'))->success();
        return back();
    }

    /**
     * Update OTP credentials
     */
    public function update_credentials(Request $request)
    {
        // Twilio credentials
        if ($request->has('twilio_sid')) {
            $this->updateBusinessSetting('TWILIO_SID', $request->twilio_sid);
        }
        if ($request->has('twilio_auth_token')) {
            $this->updateBusinessSetting('TWILIO_AUTH_TOKEN', $request->twilio_auth_token);
        }
        if ($request->has('twilio_phone_number')) {
            $this->updateBusinessSetting('VALID_TWILLO_NUMBER', $request->twilio_phone_number);
        }
        if ($request->has('twilio_type')) {
            $this->updateBusinessSetting('TWILLO_TYPE', $request->twilio_type);
        }

        // Nexmo credentials
        if ($request->has('nexmo_key')) {
            $this->updateBusinessSetting('NEXMO_KEY', $request->nexmo_key);
        }
        if ($request->has('nexmo_secret')) {
            $this->updateBusinessSetting('NEXMO_SECRET', $request->nexmo_secret);
        }

        // Fast2SMS credentials
        if ($request->has('fast2sms_api_key')) {
            $this->updateBusinessSetting('FAST2SMS_API_KEY', $request->fast2sms_api_key);
        }

        // SSL SMS credentials
        if ($request->has('ssl_sms_url')) {
            $this->updateBusinessSetting('SSL_SMS_URL', $request->ssl_sms_url);
        }
        if ($request->has('ssl_sms_user')) {
            $this->updateBusinessSetting('SSL_SMS_USER', $request->ssl_sms_user);
        }
        if ($request->has('ssl_sms_pass')) {
            $this->updateBusinessSetting('SSL_SMS_PASS', $request->ssl_sms_pass);
        }

        flash(translate('OTP credentials updated successfully'))->success();
        return back();
    }

    /**
     * Update business setting
     */
    private function updateBusinessSetting($type, $value)
    {
        $businessSetting = BusinessSetting::where('type', $type)->first();
        
        if ($businessSetting) {
            $businessSetting->value = $value;
            $businessSetting->save();
        } else {
            BusinessSetting::create([
                'type' => $type,
                'value' => $value
            ]);
        }
    }
}
