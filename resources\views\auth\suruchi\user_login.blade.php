{{--@extends('auth.layouts.authentication')--}}
@extends('frontend.suruchi.layouts.app')

@section('content')

    <div class="login__section section--padding">
        <div class="container">
            <form class="form-default account__login--inner" role="form" action="{{ route('login') }}" method="POST">
                @csrf
                <div class="login__section--inner">
                    <div class="row">
                        <div class="col-md-3 col-lg-3 col-sm-0"></div>
                        <div class="col-md-6 col-lg-6">
                            <div class="account__login">
                                <div class="account__login--header mb-25">
                                    <h2 class="account__login--header__title h3 mb-10">{{ translate('Welcome Back!')}}</h2>
                                    <p class="account__login--header__desc">Login if you are a returning customer.</p>
                                </div>
                                <div class="account__login--inner">
                                    <!-- Conditional Email/Phone Login -->
                                    @if (addon_is_activated('otp_system'))
                                        <div class="form-group phone-form-group mb-1">
                                            <input type="tel" id="phone-code" class="account__login--input" name="phone" placeholder="{{  translate('Phone') }}" value="{{ old('phone') }}" autocomplete="off">
                                        </div>
                                        <input type="hidden" name="country_code" value="">
                                        <div class="form-group email-form-group mb-1 d-none">
                                            <input type="email" id="email" class="account__login--input" name="email" placeholder="{{ translate('Email') }}" value="{{ old('email') }}" autocomplete="off">
                                            @error('email')
                                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>
                                        <button type="button" class="btn btn-link p-0 text-primary fs-12" onclick="toggleEmailPhone(this)">
                                            <i>*{{ translate('Use Email Instead') }}</i>
                                        </button>

                                        <!-- OTP Input Fields -->
                                        <div id="phone-otp-input-group" class="form-group d-none">
                                            <input type="text" id="phone_otp" name="phone_otp" class="account__login--input" placeholder="{{ translate('Enter Phone OTP') }}" autocomplete="off">
                                        </div>
                                        <div id="email-otp-input-group" class="form-group d-none">
                                            <input type="text" id="email_otp" name="email_otp" class="account__login--input" placeholder="{{ translate('Enter Email OTP') }}" autocomplete="off">
                                        </div>

                                        <!-- Send OTP Button -->
                                        <button type="button" id="send-otp-btn" class="account__login--btn primary__btn mb-3" onclick="sendOtp()">{{ translate('Send OTP') }}</button>

                                        <!-- Login Button (hidden initially) -->
                                        <button type="submit" id="login-btn" class="account__login--btn primary__btn d-none">{{ translate('Login') }}</button>
                                    @else
                                        <div class="form-group">
                                            <input type="email" id="email" class="account__login--input" placeholder="{{  translate('Email') }}" name="email" value="{{ old('email') }}" autocomplete="off">
                                            @error('email')
                                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>

                                        <!-- Password Field -->
                                        <div class="form-group">
                                            <label for="password" class="fs-12 fw-700 text-soft-dark"></label>
                                            <div class="position-relative">
                                                <input type="password" class="account__login--input" name="password" placeholder="{{ translate('Password')}}">
                                                <i class="password-toggle las la-eye"></i>
                                            </div>
                                        </div>

                                        <!-- Login Button -->
                                        <button class="account__login--btn primary__btn" type="submit">{{ translate('Login') }}</button>
                                    @endif

                                    <div class="account__login--remember__forgot mb-15 d-flex justify-content-between align-items-center">
                                        <div class="account__login--remember position__relative">
                                            <input class="checkout__checkbox--input" id="check1" type="checkbox" {{ old('remember') ? 'checked' : '' }}>
                                            <span class="checkout__checkbox--checkmark"></span>
                                            <label class="checkout__checkbox--label login__remember--label" for="check1">
                                                Remember me</label>
                                        </div>
                                        <a href="{{ route('password.request') }}" class="account__login--forgot">Forgot Your Password?</a>
                                    </div>



                                    <!-- Social Login -->
                                    @if(get_setting('google_login') || get_setting('facebook_login') || get_setting('twitter_login') || get_setting('apple_login'))
                                        <div class="account__login--divide"><span class="account__login--divide__text">OR</span></div>
                                        <div class="account__social d-flex justify-content-center mb-15">
                                            @if(get_setting('facebook_login'))
                                                <a href="{{ route('social.login', ['provider' => 'facebook']) }}" class="account__social--link facebook">Facebook</a>
                                            @endif
                                            @if(get_setting('google_login'))
                                                <a href="{{ route('social.login', ['provider' => 'google']) }}" class="account__social--link google">Google</a>
                                            @endif
                                            @if(get_setting('twitter_login'))
                                                <a href="{{ route('social.login', ['provider' => 'twitter']) }}" class="account__social--link twitter">Twitter</a>
                                            @endif
                                            @if(get_setting('apple_login'))
                                                <a href="{{ route('social.login', ['provider' => 'apple']) }}" class="account__social--link apple">Apple</a>
                                            @endif
                                        </div>
                                    @endif

                                    <!-- Register Now and Back Links -->
                                    <p class="account__login--signup__text">{{ translate("Don't have an account?") }}
                                        <a href="{{ route('user.registration') }}">Register now</a>
                                    </p>
                                    <a href="{{ url()->previous() }}" class="mt-3 fs-14 fw-700 text-primary">
                                        <i class="las la-arrow-left fs-20 mr-1"></i>{{ translate('Back to Previous Page') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('script')
    <script type="text/javascript">
        let isPhoneMode = true;

        function autoFillCustomer(){
            $('#email').val('<EMAIL>');
            $('#password').val('123456');
        }

        function toggleEmailPhone(button) {
            const phoneGroup = $('.phone-form-group');
            const emailGroup = $('.email-form-group');

            if (isPhoneMode) {
                phoneGroup.addClass('d-none');
                emailGroup.removeClass('d-none');
                button.innerHTML = '<i>*{{ translate("Use Phone Instead") }}</i>';
                isPhoneMode = false;
            } else {
                phoneGroup.removeClass('d-none');
                emailGroup.addClass('d-none');
                button.innerHTML = '<i>*{{ translate("Use Email Instead") }}</i>';
                isPhoneMode = true;
            }

            // Hide OTP inputs when switching
            $('#phone-otp-input-group').addClass('d-none');
            $('#email-otp-input-group').addClass('d-none');
            $('#login-btn').addClass('d-none');
            $('#send-otp-btn').removeClass('d-none').text('{{ translate("Send OTP") }}');
        }

        function sendOtp() {
            let data = {
                _token: "{{ csrf_token() }}"
            };

            if (isPhoneMode) {
                const phone = document.getElementById('phone-code').value;
                const countryCode = $('input[name="country_code"]').val();

                if (!phone) {
                    alert('{{ translate("Please enter phone number") }}');
                    return;
                }

                data.phone = phone;
                data.country_code = countryCode;
            } else {
                const email = document.getElementById('email').value;

                if (!email) {
                    alert('{{ translate("Please enter email") }}');
                    return;
                }

                data.email = email;
            }

            $.ajax({
                url: "{{ route('user.login.send-otp') }}",
                type: "POST",
                data: data,
                success: function(response) {
                    if (response.status === 'success') {
                        if (isPhoneMode) {
                            $('#phone-otp-input-group').removeClass('d-none');
                        } else {
                            $('#email-otp-input-group').removeClass('d-none');
                        }
                        $('#send-otp-btn').addClass('d-none');
                        $('#login-btn').removeClass('d-none');

                        // Change form action to cart login
                        $('.account__login--inner').attr('action', '{{ route("cart.login.submit") }}');

                        alert(response.message);
                    } else {
                        alert(response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    alert(response ? response.message : '{{ translate("Error sending OTP. Please try again.") }}');
                }
            });
        }
    </script>
@endsection
