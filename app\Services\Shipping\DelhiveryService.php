<?php

namespace App\Services\Shipping;

use App\Models\Order;
use App\Models\ShippingPartner;

class DelhiveryService extends AbstractShippingService
{
    public function __construct(ShippingPartner $shippingPartner = null)
    {
        if (!$shippingPartner) {
            $shippingPartner = ShippingPartner::where('code', 'delhivery')->first();
        }
        
        parent::__construct($shippingPartner);
    }
    
    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return float
     */
    public function calculateRate(Order $order, array $fromAddress, array $toAddress): float
    {
        // Implementation for Delhivery rate calculation
        // This is a placeholder - actual implementation would use the Delhivery API
        
        $weight = 0;
        
        // Calculate total weight
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;
        }
        
        // Prepare data for API request
        $data = [
            'pickup_pincode' => $fromAddress['postal_code'],
            'delivery_pincode' => $toAddress['postal_code'],
            'weight' => $weight,
            'cod' => $order->payment_type == 'cash_on_delivery' ? 1 : 0
        ];
        
        // For now, return a dummy rate
        // In production, this would call the Delhivery API
        return 120.00;
    }
    
    /**
     * Create a shipping order with Delhivery
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return array
     */
    public function createShippingOrder(Order $order, array $fromAddress, array $toAddress): array
    {
        // Implementation for Delhivery order creation
        // This is a placeholder - actual implementation would use the Delhivery API
        
        $data = [
            'format' => 'json',
            'data' => json_encode([
                'shipments' => [
                    [
                        'name' => $order->user->name,
                        'add' => $toAddress['address'],
                        'pin' => $toAddress['postal_code'],
                        'city' => $toAddress['city'],
                        'state' => $toAddress['state'],
                        'country' => $toAddress['country'],
                        'phone' => $toAddress['phone'],
                        'order' => $order->code,
                        'payment_mode' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'Prepaid',
                        'cod_amount' => $order->payment_type == 'cash_on_delivery' ? $order->grand_total : 0,
                        'total_amount' => $order->grand_total
                    ]
                ],
                'pickup_location' => [
                    'name' => $fromAddress['name'],
                    'add' => $fromAddress['address'],
                    'city' => $fromAddress['city'],
                    'state' => $fromAddress['state'],
                    'country' => $fromAddress['country'],
                    'pin' => $fromAddress['postal_code'],
                    'phone' => $fromAddress['phone']
                ]
            ])
        ];
        
        // For now, return a dummy response
        // In production, this would call the Delhivery API
        return [
            'success' => true,
            'tracking_id' => 'DL' . rand(1000000, 9999999),
            'shipment_id' => rand(10000, 99999),
            'response' => json_encode($data)
        ];
    }
    
    /**
     * Track a shipment with Delhivery
     *
     * @param string $trackingId
     * @return array
     */
    public function trackShipment(string $trackingId): array
    {
        // Implementation for Delhivery tracking
        // This is a placeholder - actual implementation would use the Delhivery API
        
        // For now, return a dummy response
        // In production, this would call the Delhivery API
        return [
            'tracking_id' => $trackingId,
            'status' => 'in_transit',
            'current_location' => 'Delhi',
            'expected_delivery' => date('Y-m-d', strtotime('+2 days')),
            'tracking_url' => 'https://www.delhivery.com/track/' . $trackingId
        ];
    }
}
