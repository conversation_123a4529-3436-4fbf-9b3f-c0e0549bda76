<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PhoneOtp extends Model
{
    protected $fillable = [
        'phone',
        'otp_code',
        'purpose',
        'expires_at',
        'is_used'
    ];

    protected $dates = [
        'expires_at'
    ];

    /**
     * Generate and save OTP for phone number
     */
    public static function generateOtp($phone, $purpose = 'login')
    {
        // Delete existing unused OTPs for this phone and purpose
        self::where('phone', $phone)
            ->where('purpose', $purpose)
            ->where('is_used', false)
            ->delete();

        // Generate new OTP
        $otpCode = rand(100000, 999999);
        
        return self::create([
            'phone' => $phone,
            'otp_code' => $otpCode,
            'purpose' => $purpose,
            'expires_at' => Carbon::now()->addMinutes(10),
            'is_used' => false
        ]);
    }

    /**
     * Verify OTP
     */
    public static function verifyOtp($phone, $otpCode, $purpose = 'login')
    {
        $otp = self::where('phone', $phone)
            ->where('otp_code', $otpCode)
            ->where('purpose', $purpose)
            ->where('is_used', false)
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if ($otp) {
            $otp->update(['is_used' => true]);
            return true;
        }

        return false;
    }

    /**
     * Check if OTP is valid
     */
    public function isValid()
    {
        return !$this->is_used && $this->expires_at > Carbon::now();
    }

    /**
     * Check if OTP is expired
     */
    public function isExpired()
    {
        return $this->expires_at <= Carbon::now();
    }
}
