<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\PhoneOtp;
use App\Services\SendSmsService;
use Carbon\Carbon;
use Auth;

class OTPVerificationController extends Controller
{
    protected $smsService;

    public function __construct()
    {
        $this->smsService = new SendSmsService();
    }

    /**
     * Show phone verification form
     */
    public function verification()
    {
        if (Auth::check() && Auth::user()->phone_verified_at) {
            return redirect()->route('dashboard');
        }

        return view('otp_systems.frontend.auth.'.get_setting('authentication_layout_select').'.phone_verification');
    }

    /**
     * Send OTP code to user's phone
     */
    public function send_code(User $user)
    {
        if (!$user->phone) {
            return false;
        }

        // Generate OTP
        $otp = PhoneOtp::generateOtp($user->phone, 'verification');
        
        // Send SMS
        $message = translate('Your verification code is: ') . $otp->otp_code;
        $this->smsService->sendSMS($user->phone, env('APP_NAME'), $message, null);

        return true;
    }

    /**
     * Send OTP for login
     */
    public function send_login_otp($phone)
    {
        // Generate OTP
        $otp = PhoneOtp::generateOtp($phone, 'login');
        
        // Send SMS
        $message = translate('Your login OTP is: ') . $otp->otp_code . translate('. Valid for 10 minutes.');
        $this->smsService->sendSMS($phone, env('APP_NAME'), $message, null);

        return $otp;
    }

    /**
     * Verify phone with OTP
     */
    public function verify_phone(Request $request)
    {
        $request->validate([
            'verification_code' => 'required|string|size:6'
        ]);

        $user = Auth::user();
        
        if (!$user) {
            flash(translate('Please login first'))->error();
            return redirect()->route('user.login');
        }

        // Verify OTP
        if (PhoneOtp::verifyOtp($user->phone, $request->verification_code, 'verification')) {
            $user->phone_verified_at = Carbon::now();
            $user->save();

            flash(translate('Phone verified successfully'))->success();
            
            if ($user->user_type == 'seller') {
                return redirect()->route('seller.dashboard');
            }
            
            return redirect()->route('dashboard');
        }

        flash(translate('Invalid or expired verification code'))->error();
        return back();
    }

    /**
     * Verify login OTP
     */
    public function verify_login_otp($phone, $otpCode)
    {
        return PhoneOtp::verifyOtp($phone, $otpCode, 'login');
    }

    /**
     * Resend verification code
     */
    public function resend_verificcation_code()
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['result' => false, 'message' => translate('Please login first')]);
        }

        if ($this->send_code($user)) {
            return response()->json(['result' => true, 'message' => translate('Verification code sent successfully')]);
        }

        return response()->json(['result' => false, 'message' => translate('Failed to send verification code')]);
    }

    /**
     * Show reset password form
     */
    public function show_reset_password_form()
    {
        return view('otp_systems.frontend.auth.'.get_setting('authentication_layout_select').'.reset_with_phone');
    }

    /**
     * Reset password with OTP
     */
    public function reset_password_with_code(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'verification_code' => 'required|string|size:6',
            'password' => 'required|string|min:6|confirmed'
        ]);

        // Verify OTP
        if (PhoneOtp::verifyOtp($request->phone, $request->verification_code, 'password_reset')) {
            $user = User::where('phone', $request->phone)->first();
            
            if ($user) {
                $user->password = bcrypt($request->password);
                $user->save();

                flash(translate('Password reset successfully'))->success();
                return redirect()->route('user.login');
            }
        }

        flash(translate('Invalid or expired verification code'))->error();
        return back();
    }
}
