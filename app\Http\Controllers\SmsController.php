<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\SendSmsService;
use App\Models\User;

class SmsController extends Controller
{
    protected $smsService;

    public function __construct()
    {
        $this->smsService = new SendSmsService();
    }

    /**
     * Show SMS sending page
     */
    public function index()
    {
        $users = User::whereNotNull('phone')->get();
        return view('backend.marketing.sms.index', compact('users'));
    }

    /**
     * Send SMS
     */
    public function send(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'message' => 'required|string|max:160'
        ]);

        try {
            $this->smsService->sendSMS(
                $request->phone,
                env('APP_NAME'),
                $request->message,
                null
            );

            flash(translate('SMS sent successfully'))->success();
        } catch (\Exception $e) {
            flash(translate('Failed to send SMS: ') . $e->getMessage())->error();
        }

        return back();
    }

    /**
     * Send bulk SMS
     */
    public function sendBulk(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'message' => 'required|string|max:160'
        ]);

        $users = User::whereIn('id', $request->user_ids)
                    ->whereNotNull('phone')
                    ->get();

        $successCount = 0;
        $failCount = 0;

        foreach ($users as $user) {
            try {
                $this->smsService->sendSMS(
                    $user->phone,
                    env('APP_NAME'),
                    $request->message,
                    null
                );
                $successCount++;
            } catch (\Exception $e) {
                $failCount++;
            }
        }

        flash(translate('SMS sent to ') . $successCount . translate(' users. Failed: ') . $failCount)->success();
        return back();
    }
}
