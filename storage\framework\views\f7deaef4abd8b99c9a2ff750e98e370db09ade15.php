<div class="admin-login-wrapper d-flex flex-column justify-content-center bg-light">
    <section class="overflow-hidden" style="min-height: 100vh;">
        <div class="row no-gutters" style="min-height: 100vh;">
            <!-- Left Side: Background Image -->
            <div class="col-xxl-9 col-lg-8">
                <div class="h-100">
                    <img src="<?php echo e(uploaded_asset(get_setting('admin_login_page_image'))); ?>"
                         alt="<?php echo e(translate('Admin Login Page Image')); ?>"
                         class="img-fit w-100 h-100">
                </div>
            </div>

            <!-- Right Side: Login Form -->
            <div class="col-xxl-3 col-lg-4 d-flex align-items-center bg-white">
                <div class="p-4 flex-grow-1">
                    <!-- Logo and Title Section -->
                    <div class="text-center mb-4">
                        <img src="<?php echo e(uploaded_asset(get_setting('site_icon'))); ?>"
                             alt="<?php echo e(translate('Site Icon')); ?>"
                             class="img-fluid mb-3">
                        <h1 class="fs-20 text-primary fw-bold"><?php echo e(translate('Welcome to')); ?> <?php echo e(env('APP_NAME')); ?></h1>
                        <p class="fs-14 text-muted"><?php echo e(translate('Login to your account')); ?></p>
                    </div>

                    <!-- Login Form -->
                    <form class="form-default" role="form" action="<?php echo e(route('login')); ?>" method="POST">
                        <?php echo csrf_field(); ?>

                        <!-- Email -->
                        <div class="form-group mb-3">
                            <label for="email" class="fs-12 fw-bold text-muted"><?php echo e(translate('Email')); ?></label>
                            <input type="email"
                                   class="form-control rounded-0<?php echo e($errors->has('email') ? ' is-invalid' : ''); ?>"
                                   name="email"
                                   value="<?php echo e(old('email')); ?>"
                                   placeholder="<?php echo e(translate('<EMAIL>')); ?>"
                                   id="email"
                                   autocomplete="off">
                            <?php if($errors->has('email')): ?>
                                <span class="invalid-feedback" role="alert">
                                    <strong><?php echo e($errors->first('email')); ?></strong>
                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- Password -->
                        <div class="form-group mb-3">
                            <label for="password" class="fs-12 fw-bold text-muted"><?php echo e(translate('Password')); ?></label>
                            <div class="position-relative">
                                <input type="password"
                                       class="form-control rounded-0<?php echo e($errors->has('password') ? ' is-invalid' : ''); ?>"
                                       placeholder="<?php echo e(translate('Password')); ?>"
                                       name="password"
                                       id="password">
                                <i class="password-toggle las la-eye la-lg position-absolute end-0 top-50 translate-middle-y pe-3 cursor-pointer"
                                   onclick="togglePasswordVisibility('password')"></i>
                            </div>
                            <?php if($errors->has('password')): ?>
                                <span class="invalid-feedback" role="alert">
                                    <strong><?php echo e($errors->first('password')); ?></strong>
                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- Remember Me and Forgot Password -->
                        <div class="row mb-4">
                            <div class="col-6">
                                <div class="form-check ps-0">
                                    <label class="d-flex align-items-center cursor-pointer">
                                        <input type="checkbox" name="remember" <?php echo e(old('remember') ? 'checked' : ''); ?> class="form-check-input me-2 mt-0">
                                        <span class="fs-12 fw-bold text-muted"><?php echo e(translate('Remember Me')); ?></span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-6 d-flex justify-content-end align-items-center">
                                <a href="<?php echo e(route('password.request')); ?>"
                                   class="fs-12 fw-medium text-primary text-decoration-none hover-underline">
                                    <?php echo e(translate('Forgot password?')); ?>

                                </a>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="mt-3">
                            <button type="submit"
                                    class="btn btn-primary w-100 rounded-0 fw-bold fs-14">
                                <?php echo e(translate('Login')); ?>

                            </button>
                        </div>
                    </form>

                    <!-- Demo Mode -->
                    <?php if(env("DEMO_MODE") == "On"): ?>
                        <div class="mt-4">
                            <div class="table-responsive">
                                <table class="table table-bordered text-center">
                                    <thead>
                                    <tr>
                                        <th><?php echo e(translate('Email')); ?></th>
                                        <th><?php echo e(translate('Password')); ?></th>
                                        <th><?php echo e(translate('Action')); ?></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td><EMAIL></td>
                                        <td>123456</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="autoFillAdmin()">
                                                <?php echo e(translate('Copy')); ?>

                                            </button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>
<?php /**PATH C:\laragon\www\CM-ECOM\resources\views/auth/suruchi/admin_login.blade.php ENDPATH**/ ?>