<?php

namespace App\Services\Shipping;

use App\Models\Order;
use App\Models\ShippingPartner;

class EcomExpressService extends AbstractShippingService
{
    public function __construct(ShippingPartner $shippingPartner = null)
    {
        if (!$shippingPartner) {
            $shippingPartner = ShippingPartner::where('code', 'ecom_express')->first();
        }
        
        parent::__construct($shippingPartner);
    }
    
    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return float
     */
    public function calculateRate(Order $order, array $fromAddress, array $toAddress): float
    {
        // Implementation for Ecom Express rate calculation
        // This is a placeholder - actual implementation would use the Ecom Express API
        
        $weight = 0;
        
        // Calculate total weight
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;
        }
        
        // Prepare data for API request
        $data = [
            'origin_pin' => $fromAddress['postal_code'],
            'destination_pin' => $toAddress['postal_code'],
            'weight' => $weight,
            'payment_mode' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'Prepaid'
        ];
        
        // For now, return a dummy rate
        // In production, this would call the Ecom Express API
        return 130.00;
    }
    
    /**
     * Create a shipping order with Ecom Express
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return array
     */
    public function createShippingOrder(Order $order, array $fromAddress, array $toAddress): array
    {
        // Implementation for Ecom Express order creation
        // This is a placeholder - actual implementation would use the Ecom Express API
        
        $data = [
            'AWB_NUMBER' => '',
            'ORDER_NUMBER' => $order->code,
            'PRODUCT' => 'PPD', // Prepaid or COD
            'CONSIGNEE' => $order->user->name,
            'CONSIGNEE_ADDRESS1' => $toAddress['address'],
            'CONSIGNEE_ADDRESS2' => '',
            'CONSIGNEE_ADDRESS3' => '',
            'DESTINATION_CITY' => $toAddress['city'],
            'PINCODE' => $toAddress['postal_code'],
            'STATE' => $toAddress['state'],
            'MOBILE' => $toAddress['phone'],
            'TELEPHONE' => '',
            'ITEM_DESCRIPTION' => 'Order #' . $order->code,
            'PIECES' => $order->orderDetails->sum('quantity'),
            'COLLECTABLE_VALUE' => $order->payment_type == 'cash_on_delivery' ? $order->grand_total : 0,
            'DECLARED_VALUE' => $order->grand_total,
            'ACTUAL_WEIGHT' => 1, // Default weight
            'DIMENSIONS' => '',
            'PICKUP_NAME' => $fromAddress['name'],
            'PICKUP_ADDRESS_LINE1' => $fromAddress['address'],
            'PICKUP_ADDRESS_LINE2' => '',
            'PICKUP_CITY' => $fromAddress['city'],
            'PICKUP_PINCODE' => $fromAddress['postal_code'],
            'PICKUP_MOBILE' => $fromAddress['phone'],
            'PICKUP_PHONE' => '',
            'RETURN_NAME' => $fromAddress['name'],
            'RETURN_ADDRESS_LINE1' => $fromAddress['address'],
            'RETURN_ADDRESS_LINE2' => '',
            'RETURN_CITY' => $fromAddress['city'],
            'RETURN_PINCODE' => $fromAddress['postal_code'],
            'RETURN_MOBILE' => $fromAddress['phone'],
            'RETURN_PHONE' => ''
        ];
        
        // For now, return a dummy response
        // In production, this would call the Ecom Express API
        return [
            'success' => true,
            'tracking_id' => 'EC' . rand(1000000, 9999999),
            'shipment_id' => rand(10000, 99999),
            'response' => json_encode($data)
        ];
    }
    
    /**
     * Track a shipment with Ecom Express
     *
     * @param string $trackingId
     * @return array
     */
    public function trackShipment(string $trackingId): array
    {
        // Implementation for Ecom Express tracking
        // This is a placeholder - actual implementation would use the Ecom Express API
        
        // For now, return a dummy response
        // In production, this would call the Ecom Express API
        return [
            'tracking_id' => $trackingId,
            'status' => 'in_transit',
            'current_location' => 'Bangalore',
            'expected_delivery' => date('Y-m-d', strtotime('+4 days')),
            'tracking_url' => 'https://ecomexpress.in/tracking/?awb_no=' . $trackingId
        ];
    }
}
