@extends('frontend.suruchi.layouts.app')

@section('content')
<div class="login__section section--padding">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="account__login">
                    <div class="account__login--header mb-25">
                        <h2 class="account__login--header__title h3 mb-10">{{ translate('Reset Password')}}</h2>
                        <p class="account__login--header__desc">{{ translate('Reset your password using phone verification') }}</p>
                    </div>
                    
                    <form action="{{ route('password.update.phone') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <label>{{ translate('Phone Number') }}</label>
                            <input type="tel" name="phone" class="account__login--input" placeholder="{{ translate('Enter your phone number') }}" required>
                        </div>
                        
                        <div class="form-group">
                            <label>{{ translate('Verification Code') }}</label>
                            <input type="text" name="verification_code" class="account__login--input" placeholder="{{ translate('Enter 6-digit code') }}" maxlength="6" required>
                        </div>
                        
                        <div class="form-group">
                            <label>{{ translate('New Password') }}</label>
                            <input type="password" name="password" class="account__login--input" placeholder="{{ translate('Enter new password') }}" required>
                        </div>
                        
                        <div class="form-group">
                            <label>{{ translate('Confirm Password') }}</label>
                            <input type="password" name="password_confirmation" class="account__login--input" placeholder="{{ translate('Confirm new password') }}" required>
                        </div>
                        
                        <button type="submit" class="account__login--btn primary__btn">{{ translate('Reset Password') }}</button>
                        
                        <div class="text-center mt-3">
                            <a href="{{ route('user.login') }}" class="text-primary">{{ translate('Back to Login') }}</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
