<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CombinedOrder;
use App\Models\BusinessSetting;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CustomerPackageController;
use App\Http\Controllers\SellerPackageController;
use App\Http\Controllers\WalletController;
use Session;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Carbon\Carbon;

class PhonePeController extends Controller
{
    private $clientId;
    private $clientVersion;
    private $clientSecret;
    private $apiEndpoint;
    private $authEndpoint;
    private $accessToken;
    private $tokenExpiresAt;

    public function __construct()
    {
        $this->clientId = env('PHONEPE_CLIENT_ID');
        $this->clientVersion = env('PHONEPE_CLIENT_VERSION');
        $this->clientSecret = env('PHONEPE_CLIENT_SECRET');

        // Set API endpoint based on sandbox mode
        if (BusinessSetting::where('type', 'phonepe_sandbox')->first()->value == 1) {
            $this->apiEndpoint = 'https://api-preprod.phonepe.com/apis/pg-sandbox';
            $this->authEndpoint = 'https://api-preprod.phonepe.com/apis/pg-sandbox/v1/oauth/token';
        } else {
            $this->apiEndpoint = 'https://api.phonepe.com/apis/pg';
            $this->authEndpoint = 'https://api.phonepe.com/apis/identity-manager/v1/oauth/token';
        }
        $this->apiEndpoint = 'https://api-preprod.phonepe.com/apis/pg-sandbox';
        $this->authEndpoint = 'https://api-preprod.phonepe.com/apis/pg-sandbox/v1/oauth/token';
    }

    /**
     * Get OAuth token from PhonePe
     */
    private function getAuthToken()
    {
        // Check if we already have a valid token
        if ($this->accessToken && $this->tokenExpiresAt && $this->tokenExpiresAt > time()) {
            return $this->accessToken;
        }

        // Request new token
        $response = Http::asForm()->post($this->authEndpoint, [
            'client_id' => $this->clientId,
            'client_version' => $this->clientVersion,
            'client_secret' => $this->clientSecret,
            'grant_type' => 'client_credentials'
        ]);

        $data = $response->json();

        if (isset($data['access_token'])) {
            $this->accessToken = $data['access_token'];
            $this->tokenExpiresAt = $data['expires_at'];
            return $this->accessToken;
        }

        return null;
    }

    public function pay(Request $request = null)
    {
        if (Session::has('payment_type')) {
            if (Session::get('payment_type') == 'cart_payment') {
                $combined_order = CombinedOrder::findOrFail(Session::get('combined_order_id'));
                $amount = round($combined_order->grand_total * 100); // Amount in paise

                // Generate a unique transaction ID
                $transactionId = 'TX' . Str::random(8) . time();

                // Create payment request
                $response = $this->initiatePayment($amount, $transactionId, $combined_order);

                if (isset($response['redirectUrl'])) {
                    // Include the PhonePe checkout script
                    $script = '<script src="https://mercury.phonepe.com/web/bundle/checkout.js"></script>';
                    $script .= '<script>
                        window.onload = function() {
                            if (typeof PhonePeCheckout !== "undefined") {
                                PhonePeCheckout.transact({
                                    url: "' . $response['redirectUrl'] . '",
                                    mode: "REDIRECT"
                                });
                            } else {
                                window.location.href = "' . $response['redirectUrl'] . '";
                            }
                        }
                    </script>';

                    return response($script);
                } else {
                    flash(translate('Something went wrong'))->error();
                    return redirect()->route('home');
                }
            } elseif (Session::get('payment_type') == 'wallet_payment') {
                $amount = round(Session::get('payment_data')['amount'] * 100); // Amount in paise

                // Generate a unique transaction ID
                $transactionId = 'TX' . Str::random(8) . time();

                // Create payment request
                $response = $this->initiatePayment($amount, $transactionId);

                if (isset($response['redirectUrl'])) {
                    // Include the PhonePe checkout script
                    $script = '<script src="https://mercury.phonepe.com/web/bundle/checkout.js"></script>';
                    $script .= '<script>
                        window.onload = function() {
                            if (typeof PhonePeCheckout !== "undefined") {
                                PhonePeCheckout.transact({
                                    url: "' . $response['redirectUrl'] . '",
                                    mode: "REDIRECT"
                                });
                            } else {
                                window.location.href = "' . $response['redirectUrl'] . '";
                            }
                        }
                    </script>';

                    return response($script);
                } else {
                    flash(translate('Something went wrong'))->error();
                    return redirect()->route('home');
                }
            } elseif (Session::get('payment_type') == 'customer_package_payment') {
                $customer_package = CustomerPackage::findOrFail(Session::get('payment_data')['customer_package_id']);
                $amount = round($customer_package->amount * 100); // Amount in paise

                // Generate a unique transaction ID
                $transactionId = 'TX' . Str::random(8) . time();

                // Create payment request
                $response = $this->initiatePayment($amount, $transactionId);

                if (isset($response['redirectUrl'])) {
                    // Include the PhonePe checkout script
                    $script = '<script src="https://mercury.phonepe.com/web/bundle/checkout.js"></script>';
                    $script .= '<script>
                        window.onload = function() {
                            if (typeof PhonePeCheckout !== "undefined") {
                                PhonePeCheckout.transact({
                                    url: "' . $response['redirectUrl'] . '",
                                    mode: "REDIRECT"
                                });
                            } else {
                                window.location.href = "' . $response['redirectUrl'] . '";
                            }
                        }
                    </script>';

                    return response($script);
                } else {
                    flash(translate('Something went wrong'))->error();
                    return redirect()->route('home');
                }
            } elseif (Session::get('payment_type') == 'seller_package_payment') {
                $seller_package = SellerPackage::findOrFail(Session::get('payment_data')['seller_package_id']);
                $amount = round($seller_package->amount * 100); // Amount in paise

                // Generate a unique transaction ID
                $transactionId = 'TX' . Str::random(8) . time();

                // Create payment request
                $response = $this->initiatePayment($amount, $transactionId);

                if (isset($response['redirectUrl'])) {
                    // Include the PhonePe checkout script
                    $script = '<script src="https://mercury.phonepe.com/web/bundle/checkout.js"></script>';
                    $script .= '<script>
                        window.onload = function() {
                            if (typeof PhonePeCheckout !== "undefined") {
                                PhonePeCheckout.transact({
                                    url: "' . $response['redirectUrl'] . '",
                                    mode: "REDIRECT"
                                });
                            } else {
                                window.location.href = "' . $response['redirectUrl'] . '";
                            }
                        }
                    </script>';

                    return response($script);
                } else {
                    flash(translate('Something went wrong'))->error();
                    return redirect()->route('home');
                }
            }
        }
    }

    private function initiatePayment($amount, $transactionId, $combined_order = null)
    {
        // Get auth token
        $token = $this->getAuthToken();
        if (!$token) {
            return ['error' => 'Failed to get authentication token'];
        }

        $callbackUrl = route('phonepe.callback');

        $payload = [
            'merchantOrderId' => $transactionId,
            'amount' => $amount,
            'expireAfter' => 1200, // 20 minutes
            'paymentFlow' => [
                'type' => 'PG_CHECKOUT',
                'message' => 'Payment for order',
                'merchantUrls' => [
                    'redirectUrl' => $callbackUrl
                ]
            ]
        ];

        // Add meta info if combined order exists
        if ($combined_order) {
            $payload['metaInfo'] = [
                'udf1' => 'order_id:' . $combined_order->id,
                'udf2' => 'payment_type:cart_payment'
            ];
        } else if (Session::has('payment_type')) {
            $payload['metaInfo'] = [
                'udf1' => 'payment_type:' . Session::get('payment_type')
            ];
        }

        // Make API request
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'O-Bearer ' . $token
        ])->post($this->apiEndpoint . '/checkout/v2/pay', $payload);

        return $response->json();
    }

    public function callback(Request $request)
    {
        // Get the order ID from the request
        $orderId = $request->input('orderId');

        // Verify payment status
        $response = $this->checkPaymentStatus($orderId);

        if (isset($response['state']) && $response['state'] === 'COMPLETED') {
            $payment_details = json_encode($response);

            if (Session::has('payment_type')) {
                if (Session::get('payment_type') == 'cart_payment') {
                    return (new CheckoutController)->checkout_done(Session::get('combined_order_id'), $payment_details);
                } elseif (Session::get('payment_type') == 'wallet_payment') {
                    return (new WalletController)->wallet_payment_done(Session::get('payment_data'), $payment_details);
                } elseif (Session::get('payment_type') == 'customer_package_payment') {
                    return (new CustomerPackageController)->purchase_payment_done(Session::get('payment_data'), $payment_details);
                } elseif (Session::get('payment_type') == 'seller_package_payment') {
                    return (new SellerPackageController)->purchase_payment_done(Session::get('payment_data'), $payment_details);
                }
            }
        }

        flash(translate('Payment failed'))->error();
        return redirect()->route('home');
    }

    private function checkPaymentStatus($orderId)
    {
        // Get auth token
        $token = $this->getAuthToken();
        if (!$token) {
            return ['error' => 'Failed to get authentication token'];
        }

        // Make API request
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'O-Bearer ' . $token
        ])->get($this->apiEndpoint . '/checkout/v2/order/' . $orderId);

        return $response->json();
    }

    public function credentials_index()
    {
        return view('backend.setup_configurations.phonepe_credential');
    }

    public function update_credentials(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'phonepe_sandbox')->first();

        if ($business_settings != null) {
            if ($request->has('phonepe_sandbox')) {
                $business_settings->value = 1;
                $business_settings->save();
            } else {
                $business_settings->value = 0;
                $business_settings->save();
            }
        } else {
            $business_settings = new BusinessSetting;
            $business_settings->type = 'phonepe_sandbox';
            $business_settings->value = $request->has('phonepe_sandbox') ? 1 : 0;
            $business_settings->save();
        }

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    private function overWriteEnvFile($type, $val)
    {
        $path = base_path('.env');
        if (file_exists($path)) {
            $val = '"'.trim($val).'"';
            if(is_numeric(strpos(file_get_contents($path), $type)) && strpos(file_get_contents($path), $type) >= 0){
                file_put_contents($path, str_replace(
                    $type.'="'.env($type).'"', $type.'='.$val, file_get_contents($path)
                ));
            }
            else{
                file_put_contents($path, file_get_contents($path)."\r\n".$type.'='.$val);
            }
        }
    }
}
